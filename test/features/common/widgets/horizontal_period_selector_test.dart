import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/common/widgets/horizontal_period_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for comprehensive testing
class MockTimePeriodNotifier extends Mock implements TimePeriodNotifier {}

class MockMethodChannel extends Mock implements MethodChannel {}

// Test notifier for controlled state
class TestTimePeriodNotifier extends TimePeriodNotifier {
  TestTimePeriodNotifier(this._initialPeriod);

  final TimePeriod _initialPeriod;

  @override
  TimePeriod build() => _initialPeriod;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    state = period;
  }
}

// Test notifier with tracking capabilities
class TestTrackingTimePeriodNotifier extends TimePeriodNotifier {
  TestTrackingTimePeriodNotifier(
    this._initialPeriod, {
    required this.onSelectPeriod,
  });

  final TimePeriod _initialPeriod;
  final void Function(TimePeriod) onSelectPeriod;

  @override
  TimePeriod build() => _initialPeriod;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    onSelectPeriod(period);
    state = period;
  }
}

// Test callback tracker
class CallbackTracker {
  int callCount = 0;
  void call() => callCount++;
}

void main() {
  setUpAll(() {
    // Register fallback values for Mocktail
    registerFallbackValue(
      TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      ),
    );
  });

  group('HorizontalPeriodSelector', () {
    testWidgets('should render without errors', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
    });

    testWidgets('should display compact period formats', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should find period text in compact format (e.g., "Jan 25", "Feb 25", etc.)
      final currentMonth = TimePeriodService.getCurrentMonth();
      final compactText = TimePeriodService.formatPeriodCompact(currentMonth);

      expect(find.text(compactText), findsOneWidget);
    });

    testWidgets('should handle period selection', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find a period that's not the current one and tap it
      final pageView = find.byType(PageView);
      expect(pageView, findsOneWidget);

      // The widget should be scrollable
      await tester.drag(pageView, const Offset(-100, 0));
      await tester.pumpAndSettle();

      // This basic test ensures the widget renders and is interactive
    });

    testWidgets('should respect minimum height for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(height: 44),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final sizedBox = tester.widget<SizedBox>(
        find
            .descendant(
              of: find.byType(HorizontalPeriodSelector),
              matching: find.byType(SizedBox),
            )
            .first,
      );

      expect(sizedBox.height, equals(44.0));
    });

    testWidgets('should have proper semantics for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that the widget has proper semantics
      expect(
        find.bySemanticsLabel('Time period selector'),
        findsOneWidget,
      );
    });

    test('should format periods correctly using service', () {
      final testPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      );

      final formatted = TimePeriodService.formatPeriodCompact(testPeriod);
      expect(formatted, equals('Jan 25'));
    });

    group('State Synchronization', () {
      testWidgets('synchronizes with external period changes', (tester) async {
        final initialPeriod = TimePeriod(
          type: PeriodType.monthly,
          startDate: DateTime(2025, 1, 1),
          endDate: DateTime(2025, 1, 31),
          displayName: 'January 2025',
          dateRangeText: '01 Jan - 31 Jan',
          year: 2025,
          month: 1,
        );

        final testNotifier = TestTimePeriodNotifier(initialPeriod);

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Change period externally
        final newPeriod = TimePeriod(
          type: PeriodType.monthly,
          startDate: DateTime(2025, 3, 1),
          endDate: DateTime(2025, 3, 31),
          displayName: 'March 2025',
          dateRangeText: '01 Mar - 31 Mar',
          year: 2025,
          month: 3,
        );

        await testNotifier.selectPeriod(newPeriod);
        await tester.pumpAndSettle();

        // Verify the selector updated to show the new period
        final compactText = TimePeriodService.formatPeriodCompact(newPeriod);
        expect(find.text(compactText), findsOneWidget);
      });

      testWidgets('prevents infinite loops during synchronization', (
        tester,
      ) async {
        final initialPeriod = TimePeriodService.getCurrentMonth();

        var selectPeriodCallCount = 0;

        // Override selectPeriod to count calls
        final trackedNotifier = TestTrackingTimePeriodNotifier(
          initialPeriod,
          onSelectPeriod: (period) => selectPeriodCallCount++,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => trackedNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Multiple external updates shouldn't cause infinite loops
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pump();
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pump();
        await trackedNotifier.selectPeriod(initialPeriod);
        await tester.pumpAndSettle();

        // Should handle gracefully without excessive calls
        expect(selectPeriodCallCount, lessThan(10));
      });

      testWidgets('handles rapid period changes gracefully', (tester) async {
        final testNotifier = TestTimePeriodNotifier(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => testNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate rapid period changes
        final periods = TimePeriodService.getHorizontalPeriods();
        for (var i = 0; i < 5 && i < periods.length; i++) {
          await testNotifier.selectPeriod(periods[i]);
          await tester.pump(const Duration(milliseconds: 50));
        }

        // Allow time for state changes to process without waiting for all animations to settle
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));

        // Should handle without crashes
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });
    });

    group('User Interaction', () {
      testWidgets('responds to tap gestures', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: SizedBox(
                  width: 400,
                  height: 100,
                  child: HorizontalPeriodSelector(),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find the PageView which contains the period items
        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Verify the widget is interactive by performing a drag gesture
        await tester.drag(pageView, const Offset(-200, 0));

        // Allow time for the drag to process
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 200));

        // The widget should still be present and functional after interaction
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
        expect(find.byType(PageView), findsOneWidget);
      });

      testWidgets('handles swipe gestures for navigation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Test left swipe (next period)
        await tester.drag(pageView, const Offset(-200, 0));
        await tester.pumpAndSettle();

        // Test right swipe (previous period)
        await tester.drag(pageView, const Offset(200, 0));
        await tester.pumpAndSettle();

        // Should handle swipes without errors
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('provides smooth scroll animation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find the PageView to trigger scroll animation
        final pageView = find.byType(PageView);
        expect(pageView, findsOneWidget);

        // Drag to trigger smooth scroll animation
        await tester.drag(pageView, const Offset(-150, 0));

        // Verify animation is in progress
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Allow animation to process without waiting for complete settle
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });
    });

    group('Future Navigation Support', () {
      testWidgets('respects allowFutureNavigation flag', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(
                  allowFutureNavigation: true,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final widget = tester.widget<HorizontalPeriodSelector>(
          find.byType(HorizontalPeriodSelector),
        );
        expect(widget.allowFutureNavigation, isTrue);
      });

      testWidgets('limits future navigation when disabled', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(
                  allowFutureNavigation: false,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final widget = tester.widget<HorizontalPeriodSelector>(
          find.byType(HorizontalPeriodSelector),
        );
        expect(widget.allowFutureNavigation, isFalse);

        // Widget should still render periods correctly
        expect(find.byType(PageView), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides comprehensive semantic labels', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Main selector semantics
        expect(
          find.bySemanticsLabel('Time period selector'),
          findsOneWidget,
        );

        // Period item semantics
        final semantics = tester.getSemantics(
          find.byType(HorizontalPeriodSelector),
        );

        // Should have semantic information for accessibility
        expect(semantics, isNotNull);
      });

      testWidgets('supports screen reader navigation', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find semantic widgets for period items
        final semanticWidgets = find.descendant(
          of: find.byType(HorizontalPeriodSelector),
          matching: find.byType(Semantics),
        );

        expect(semanticWidgets, findsAtLeastNWidgets(1));

        // Each period item should have button semantics
        for (final widget in semanticWidgets.evaluate()) {
          final semanticsWidget = widget.widget as Semantics;
          if (semanticsWidget.properties.label?.contains('period') ?? false) {
            expect(semanticsWidget.properties.button, isTrue);
          }
        }
      });

      testWidgets('maintains minimum tap target size', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(height: 44),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Height should meet accessibility guidelines
        final sizedBox = tester.widget<SizedBox>(
          find.byType(SizedBox).first,
        );
        expect(sizedBox.height, greaterThanOrEqualTo(44.0));
      });
    });

    group('Performance', () {
      testWidgets('handles large period lists efficiently', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        // Initial render should be fast
        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Scroll through many periods quickly
        final pageView = find.byType(PageView);
        for (var i = 0; i < 5; i++) {
          await tester.drag(pageView, const Offset(-100, 0));
          await tester.pump(const Duration(milliseconds: 16));
        }

        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('lazy loads periods correctly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // PageView should be used for efficient rendering
        expect(find.byType(PageView), findsOneWidget);

        // Should contain multiple period items for performance test
        expect(find.byType(Container), findsAtLeastNWidgets(1));
      });

      testWidgets('disposes resources properly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Remove widget
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: SizedBox(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Widget should be removed without errors
        expect(find.byType(HorizontalPeriodSelector), findsNothing);
      });
    });

    group('Visual States', () {
      testWidgets('highlights selected period correctly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should have at least one container with selection styling
        final containers = find.byType(Container);
        expect(containers, findsAtLeastNWidgets(1));
      });

      testWidgets('shows current month indicator', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Current month should be highlighted differently
        final currentMonth = TimePeriodService.getCurrentMonth();
        final compactText = TimePeriodService.formatPeriodCompact(currentMonth);

        // Should find current month text
        expect(find.text(compactText), findsOneWidget);
      });

      testWidgets('animates between states smoothly', (tester) async {
        await tester.pumpWidget(
          const ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find animated containers
        final animatedContainers = find.byType(AnimatedContainer);
        expect(animatedContainers, findsAtLeastNWidgets(1));

        // Verify animation duration
        final animatedContainer = tester.widget<AnimatedContainer>(
          animatedContainers.first,
        );
        expect(
          animatedContainer.duration,
          equals(const Duration(milliseconds: 200)),
        );
        expect(animatedContainer.curve, equals(Curves.easeInOut));
      });
    });

    group('Error Handling', () {
      testWidgets('handles period selection errors gracefully', (tester) async {
        final mockNotifier = MockTimePeriodNotifier();
        when(() => mockNotifier.selectPeriod(any())).thenThrow(
          Exception('Network error'),
        );
        when(mockNotifier.build).thenReturn(
          TimePeriodService.getCurrentMonth(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              timePeriodNotifierProvider.overrideWith(() => mockNotifier),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: HorizontalPeriodSelector(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Widget should render despite errors
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Try to interact with widget - should not crash
        final pageView = find.byType(PageView);
        if (pageView.evaluate().isNotEmpty) {
          await tester.drag(pageView, const Offset(-50, 0));
          await tester.pumpAndSettle();
        }

        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      });

      testWidgets('handles initialization edge cases', (tester) async {
        // Test with empty provider scope
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        );

        // Should handle missing provider gracefully
        expect(tester.takeException(), isNotNull);
      });
    });
  });
}
