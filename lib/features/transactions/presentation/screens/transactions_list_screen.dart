import 'dart:async';

import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/transactions/presentation/widgets/empty_transactions_state.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/features/transactions/services/transaction_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen displaying a list of all user transactions with filtering and management options
class TransactionsListScreen extends ConsumerStatefulWidget {
  const TransactionsListScreen({super.key, this.categoryId, this.accountId});

  /// Optional category ID to filter transactions by
  final String? categoryId;

  /// Optional account ID to filter transactions by
  final String? accountId;

  @override
  ConsumerState<TransactionsListScreen> createState() =>
      _TransactionsListScreenState();
}

class _TransactionsListScreenState
    extends ConsumerState<TransactionsListScreen> {
  String _searchQuery = '';
  TransactionType? _selectedType;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final transactionsAsync = ref.watch(transactionListProvider);
    final appBarWidget = _buildAppBar(theme, l10n);

    // Watch time period to trigger rebuilds when it changes
    ref.watch(timePeriodNotifierProvider);

    // Check if this is a filtered view (category or account specific)
    final isFilteredView =
        widget.categoryId != null || widget.accountId != null;

    return Scaffold(
      // Only set appBar for filtered views (traditional AppBar)
      appBar: isFilteredView ? appBarWidget as PreferredSizeWidget : null,
      body: Column(
        children: [
          // Show horizontal app bar when not in filtered view
          if (!isFilteredView) appBarWidget,

          // Main content
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                ref.invalidate(transactionListProvider);
              },
              child: transactionsAsync.when(
                data: (transactions) =>
                    _buildTransactionsList(transactions, theme, l10n),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorState(error, theme, l10n),
              ),
            ),
          ),
        ],
      ),
    ).withGlobalFabs();
  }

  Widget _buildAppBar(ThemeData theme, AppLocalizations l10n) {
    if (widget.categoryId != null) {
      // Show category-specific title
      final categoryAsync = ref.watch(categoryProvider(widget.categoryId!));
      return categoryAsync.when(
        data: (category) => AppBarHelpers.createStandardScrollableAppBar(
          title: category != null
              ? '${l10n.transactions} - ${category.name}'
              : l10n.transactions,
          automaticallyImplyLeading:
              false, // Use global FAB back button instead
        ),
        loading: () => AppBarHelpers.createStandardScrollableAppBar(
          title: l10n.transactions,
          automaticallyImplyLeading:
              false, // Use global FAB back button instead
        ),
        error: (_, _) => AppBarHelpers.createStandardScrollableAppBar(
          title: l10n.transactions,
          automaticallyImplyLeading:
              false, // Use global FAB back button instead
        ),
      );
    }

    if (widget.accountId != null) {
      // Show account-specific title
      final accountAsync = ref.watch(accountProvider(widget.accountId!));
      return accountAsync.when(
        data: (account) => AppBarHelpers.createStandardScrollableAppBar(
          title: account != null
              ? '${l10n.transactions} - ${account.name}'
              : l10n.transactions,
        ),
        loading: () => AppBarHelpers.createStandardScrollableAppBar(
          title: l10n.transactions,
        ),
        error: (_, _) => AppBarHelpers.createStandardScrollableAppBar(
          title: l10n.transactions,
        ),
      );
    }

    // Default header with horizontal TimePeriodSelector and filters
    return AppBarHelpers.createHorizontalTimePeriodAppBar(
      title: l10n.transactions,
      automaticallyImplyLeading: false, // Remove back button
      actions: [
        IconButton(
          onPressed: _showFilterBottomSheet,
          icon: const Icon(Icons.filter_list),
          tooltip: l10n.filterTransactions,
        ),
        IconButton(
          onPressed: _showSearchBottomSheet,
          icon: const Icon(Icons.search),
          tooltip: l10n.searchTransactions,
        ),
      ],
    );
  }

  Widget _buildTransactionsList(
    List<Transaction> transactions,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    if (transactions.isEmpty) {
      return const EmptyTransactionsState();
    }

    // Apply filters
    final filteredTransactions = _applyFilters(transactions);

    if (filteredTransactions.isEmpty) {
      return _buildNoResultsState(theme, l10n);
    }

    // Sort by date (newest first)
    filteredTransactions.sort((a, b) {
      final aDate = a.transactionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bDate = b.transactionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bDate.compareTo(aDate);
    });

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = filteredTransactions[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.sm),
          child: TransactionCard(
            transaction: transaction,
            onTap: () => context.push('/transactions/${transaction.id}/edit'),
            showActions: true,
            onEdit: () => _handleEditTransaction(context, transaction),
            onDelete: () => _handleDeleteTransaction(context, ref, transaction),
          ),
        );
      },
    );
  }

  List<Transaction> _applyFilters(List<Transaction> transactions) {
    var filtered = transactions;

    // Apply time period filter (global state)
    final selectedPeriod = ref.read(timePeriodNotifierProvider);
    filtered = filtered.where((transaction) {
      final transactionDate = transaction.transactionDate;
      if (transactionDate == null) return false;

      return transactionDate.isAfter(
            selectedPeriod.startDate.subtract(const Duration(days: 1)),
          ) &&
          transactionDate.isBefore(
            selectedPeriod.endDate.add(const Duration(days: 1)),
          );
    }).toList();

    // Apply category filter (if provided)
    if (widget.categoryId != null) {
      filtered = filtered
          .where((transaction) => transaction.categoryId == widget.categoryId)
          .toList();
    }

    // Apply account filter (if provided)
    if (widget.accountId != null) {
      filtered = filtered
          .where(
            (transaction) =>
                transaction.fromAccountId == widget.accountId ||
                transaction.toAccountId == widget.accountId,
          )
          .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final query = _searchQuery.toLowerCase();
        return (transaction.description?.toLowerCase().contains(query) ??
                false) ||
            (transaction.notes?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply type filter
    if (_selectedType != null) {
      filtered = filtered
          .where((transaction) => transaction.type == _selectedType)
          .toList();
    }

    return filtered;
  }

  Widget _buildNoResultsState(ThemeData theme, AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            l10n.noTransactionsFound,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            l10n.tryAdjustingFilters,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          FilledButton(
            onPressed: _clearFilters,
            child: Text(l10n.clearFilters),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(
    Object error,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: AppSpacing.md),
          Text(
            l10n.errorLoadingTransactions,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          FilledButton(
            onPressed: () => ref.invalidate(transactionListProvider),
            child: Text(l10n.retry),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      builder: (context) => _FilterBottomSheet(
        selectedType: _selectedType,
        onTypeChanged: (type) {
          setState(() => _selectedType = type);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showSearchBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      builder: (context) => _SearchBottomSheet(
        initialQuery: _searchQuery,
        onSearchChanged: (query) {
          setState(() => _searchQuery = query);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedType = null;
    });
  }

  Future<void> _handleEditTransaction(
    BuildContext context,
    Transaction transaction,
  ) async {
    // Navigate to full screen edit
    if (context.mounted) {
      unawaited(context.push('/transactions/${transaction.id}/edit'));
    }
  }

  void _handleDeleteTransaction(
    BuildContext context,
    WidgetRef ref,
    Transaction transaction,
  ) {
    final l10n = AppLocalizations.of(context)!;

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteTransaction),
        content: Text(l10n.deleteTransactionConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () =>
                _confirmDeleteTransaction(context, ref, transaction, l10n),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDeleteTransaction(
    BuildContext context,
    WidgetRef ref,
    Transaction transaction,
    AppLocalizations l10n,
  ) async {
    final theme = Theme.of(context);

    // Close the confirmation dialog
    Navigator.pop(context);

    // Show immediate success message
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.transactionDeleted),
          backgroundColor: theme.colorScheme.primary,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Perform the deletion in background
    try {
      await ref
          .read(transactionDeleterProvider.notifier)
          .deleteTransaction(transaction.userId, transaction.id);
    } on Exception catch (error) {
      // Show error message if deletion fails
      if (context.mounted) {
        final errorMessage = TransactionErrorService.getDeleteErrorMessage(
          error,
          l10n,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: theme.colorScheme.error,
            behavior: SnackBarBehavior.floating,
            action: TransactionErrorService.isRetryable(error)
                ? SnackBarAction(
                    label: l10n.retry,
                    textColor: theme.colorScheme.onError,
                    onPressed: () =>
                        _handleDeleteTransaction(context, ref, transaction),
                  )
                : null,
          ),
        );
      }
    }
  }
}

class _FilterBottomSheet extends StatelessWidget {
  const _FilterBottomSheet({
    required this.selectedType,
    required this.onTypeChanged,
  });
  final TransactionType? selectedType;
  final ValueChanged<TransactionType?> onTypeChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(l10n.filterTransactions, style: theme.textTheme.headlineSmall),
          const SizedBox(height: AppSpacing.lg),
          Text(l10n.transactionType, style: theme.textTheme.titleMedium),
          const SizedBox(height: AppSpacing.sm),
          Wrap(
            spacing: AppSpacing.sm,
            children: [
              FilterChip(
                label: Text(l10n.all),
                selected: selectedType == null,
                onSelected: (_) => onTypeChanged(null),
              ),
              FilterChip(
                label: Text(l10n.income),
                selected: selectedType == TransactionType.income,
                onSelected: (_) => onTypeChanged(TransactionType.income),
              ),
              FilterChip(
                label: Text(l10n.expense),
                selected: selectedType == TransactionType.expense,
                onSelected: (_) => onTypeChanged(TransactionType.expense),
              ),
              FilterChip(
                label: Text(l10n.transfer),
                selected: selectedType == TransactionType.transfer,
                onSelected: (_) => onTypeChanged(TransactionType.transfer),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.lg),
        ],
      ),
    );
  }
}

class _SearchBottomSheet extends StatefulWidget {
  const _SearchBottomSheet({
    required this.initialQuery,
    required this.onSearchChanged,
  });
  final String initialQuery;
  final ValueChanged<String> onSearchChanged;

  @override
  State<_SearchBottomSheet> createState() => _SearchBottomSheetState();
}

class _SearchBottomSheetState extends State<_SearchBottomSheet> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: EdgeInsets.only(
        left: AppSpacing.lg,
        right: AppSpacing.lg,
        top: AppSpacing.lg,
        bottom: MediaQuery.of(context).viewInsets.bottom + AppSpacing.lg,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(l10n.searchTransactions, style: theme.textTheme.headlineSmall),
          const SizedBox(height: AppSpacing.lg),
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              labelText: l10n.searchTransactions,
              hintText: l10n.searchTransactionsHint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      onPressed: () => _controller.clear(),
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            autofocus: true,
            onChanged: (_) => setState(() {}),
          ),
          const SizedBox(height: AppSpacing.lg),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.cancel),
              ),
              const SizedBox(width: AppSpacing.sm),
              FilledButton(
                onPressed: () => widget.onSearchChanged(_controller.text),
                child: Text(l10n.search),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
